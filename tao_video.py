#!/usr/bin/env python3
"""
Script tạo video với Wan 2.2 5B trên <PERSON>
Hỗ trợ tạo video từ text hoặc từ image
"""

import argparse
import sys
import subprocess
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="Tạo video với Wan 2.2 5B trên <PERSON>")
    
    # Tham số chính
    parser.add_argument("--prompt", "-p", required=True,
                       help="Mô tả video bằng tiếng Việt hoặc tiếng Anh")
    parser.add_argument("--image", "-i", 
                       help="Đường dẫn đến ảnh đầu vào (cho image-to-video)")
    
    # Tham số video
    parser.add_argument("--size", "-s", default="1280*704",
                       choices=["1280*704", "704*1280", "1024*1024"],
                       help="Kích thước video (mặc định: 1280*704 - 720P ngang)")
    parser.add_argument("--seed", type=int, default=42,
                       help="Seed ngẫu nhiên (mặc định: 42)")
    parser.add_argument("--frames", type=int, default=121,
                       help="Số frame (121 = 5 giây ở 24fps)")
    
    # Tham số khác
    parser.add_argument("--template", "-t",
                       choices=["landscape", "portrait", "action", "animal"],
                       help="Sử dụng template prompt có sẵn")
    parser.add_argument("--preview", action="store_true",
                       help="Chỉ hiển thị prompt sẽ được sử dụng")
    
    args = parser.parse_args()
    
    # Xử lý template
    if args.template:
        templates = {
            "landscape": "Một cảnh quan thiên nhiên đẹp với {}, chất lượng cao, chi tiết sắc nét, ánh sáng đẹp",
            "portrait": "Chân dung của {}, ánh sáng đẹp, chất lượng điện ảnh, chi tiết sắc nét",
            "action": "Cảnh hành động {}, chuyển động mượt mà, góc quay đẹp, chất lượng cao",
            "animal": "Một con vật {} đang chuyển động tự nhiên, môi trường đẹp, chất lượng cao"
        }
        final_prompt = templates[args.template].format(args.prompt)
    else:
        final_prompt = args.prompt
    
    # Hiển thị thông tin
    print("🎬 Tạo Video với Wan 2.2 5B")
    print("=" * 50)
    print(f"📝 Prompt: {final_prompt}")
    print(f"📐 Kích thước: {args.size}")
    print(f"🎞️ Số frame: {args.frames} ({args.frames/24:.1f} giây)")
    print(f"🎲 Seed: {args.seed}")
    
    if args.image:
        print(f"🖼️ Ảnh đầu vào: {args.image}")
        if not Path(args.image).exists():
            print(f"❌ Không tìm thấy file ảnh: {args.image}")
            sys.exit(1)
        print("🎥 Chế độ: Image-to-Video")
    else:
        print("🎥 Chế độ: Text-to-Video")
    
    if args.preview:
        print("\n✅ Preview hoàn tất!")
        return
    
    print("\n🚀 Bắt đầu tạo video trên Modal...")
    print("⏱️ Quá trình này có thể mất 5-15 phút...")
    
    try:
        # Xây dựng lệnh Modal
        if args.image:
            # Image-to-Video
            cmd = [
                "modal", "run", "wan22_modal.py::image_to_video",
                "--prompt", final_prompt,
                "--image_path", args.image,
                "--size", args.size,
                "--seed", str(args.seed)
            ]
        else:
            # Text-to-Video
            cmd = [
                "modal", "run", "wan22_modal.py::text_to_video", 
                "--prompt", final_prompt,
                "--size", args.size,
                "--seed", str(args.seed)
            ]
        
        # Chạy lệnh
        result = subprocess.run(cmd, check=True)
        print("\n✅ Tạo video thành công!")
        print("📁 Video đã được lưu trong Modal volume 'wan22-outputs'")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Lỗi khi tạo video: {e}")
        print("💡 Kiểm tra:")
        print("   - Modal đã được cài đặt và xác thực chưa?")
        print("   - App đã được deploy chưa?")
        print("   - Model đã được tải xuống chưa?")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ Không tìm thấy Modal CLI")
        print("💡 Cài đặt Modal: pip install modal")
        sys.exit(1)

def show_examples():
    """Hiển thị các ví dụ sử dụng"""
    examples = [
        {
            "title": "Text-to-Video cơ bản",
            "cmd": "python tao_video.py --prompt 'Một chú mèo trắng đang chơi trên bãi biển'"
        },
        {
            "title": "Sử dụng template landscape", 
            "cmd": "python tao_video.py --template landscape --prompt 'núi cao và thác nước'"
        },
        {
            "title": "Image-to-Video",
            "cmd": "python tao_video.py --image anh.jpg --prompt 'chuyển động nhẹ nhàng'"
        },
        {
            "title": "Video dọc (portrait)",
            "cmd": "python tao_video.py --prompt 'người đang nhảy' --size 704*1280"
        },
        {
            "title": "Preview prompt",
            "cmd": "python tao_video.py --template action --prompt 'xe đua' --preview"
        }
    ]
    
    print("📚 Ví dụ sử dụng:")
    print("=" * 50)
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   {example['cmd']}")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🎥 Script Tạo Video Wan 2.2 5B")
        print("Sử dụng: python tao_video.py --help để xem hướng dẫn")
        print()
        show_examples()
    else:
        main()
