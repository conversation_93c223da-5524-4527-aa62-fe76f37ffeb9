{"1": {"inputs": {"text": "chuyển động nhẹ nhàng, t<PERSON>, chất l<PERSON><PERSON><PERSON> cao", "model_name": "Wan2.2-TI2V-5B"}, "class_type": "WanTextEncoder", "_meta": {"title": "Text Encoder"}}, "2": {"inputs": {"image": "input_image.jpg"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "3": {"inputs": {"image": ["2", 0], "num_frames": 121, "fps": 24, "seed": 42}, "class_type": "WanImageToVideoLatent", "_meta": {"title": "Image to Video Latent"}}, "4": {"inputs": {"model_path": "/root/models/Wan2.2-TI2V-5B", "task": "ti2v-5B", "offload_model": true, "convert_model_dtype": true, "t5_cpu": true}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "<PERSON> Loader"}}, "5": {"inputs": {"model": ["4", 0], "text_encoding": ["1", 0], "latent": ["3", 0], "steps": 50, "cfg": 7.5, "sampler_name": "ddim", "scheduler": "linear"}, "class_type": "<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "<PERSON>"}}, "6": {"inputs": {"model": ["4", 0], "samples": ["5", 0]}, "class_type": "WanVAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"video": ["6", 0], "filename_prefix": "wan22_i2v", "format": "mp4", "fps": 24, "quality": "high"}, "class_type": "SaveVideo", "_meta": {"title": "Save Video"}}}