@echo off
chcp 65001 >nul
echo 🚀 Cài Đặt Wan 2.2 5B tr<PERSON><PERSON> (Windows)
echo ==========================================

echo 📦 Đang cài đặt requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Không thể cài đặt requirements
    pause
    exit /b 1
)

echo 🔐 Kiểm tra xác thực Modal...
modal token current >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Modal chưa được xác thực
    echo Vui lòng chạy: modal token new
    pause
    exit /b 1
)

echo ✅ Modal đã được xác thực

echo 🚀 Chạy script cài đặt Python...
python cai_dat_wan22.py
if %errorlevel% neq 0 (
    echo ❌ Cài đặt thất bại
    pause
    exit /b 1
)

echo 🧪 Chạy kiểm tra hệ thống...
python kiem_tra_wan22.py

echo ✅ Cài đặt hoàn tất!
echo.
echo Các bước tiếp theo:
echo 1. python tao_video.py --prompt "mô tả video của bạn"
echo 2. Kiểm tra Modal dashboard: https://modal.com/apps
echo.
pause
