"""
<PERSON><PERSON><PERSON> hình <PERSON> 2.2 5B trên <PERSON>
"""

# <PERSON><PERSON><PERSON> hình <PERSON>
MODAL_APP_NAME = "wan22-video-app"
MODAL_GPU = "A100"  # A100 40GB cho Wan 2.2 5B
MODAL_TIMEOUT = 7200  # 2 giờ
MODAL_IDLE_TIMEOUT = 600  # 10 phút

# Cấu hình <PERSON> 2.2
WAN22_MODEL_NAME = "Wan-AI/Wan2.2-TI2V-5B"
WAN22_TASK = "ti2v-5B"

# Cài đặt tạo video mặc định
DEFAULT_VIDEO_SETTINGS = {
    "size": "1280*704",  # 720P cho Wan 2.2
    "num_frames": 121,   # 5 giây ở 24fps
    "fps": 24,
    "seed": 42
}

# Các kích thước video được hỗ trợ
SUPPORTED_SIZES = {
    "720p_landscape": "1280*704",
    "720p_portrait": "704*1280", 
    "square": "1024*1024"
}

# Đường dẫn
PATHS = {
    "wan22_repo": "/root/Wan2.2",
    "models": "/root/models",
    "outputs": "/root/outputs",
    "model_path": "/root/models/Wan2.2-TI2V-5B"
}

# Volume names
VOLUMES = {
    "models": "wan22-models",
    "outputs": "wan22-outputs"
}

# Prompt templates tiếng Việt
PROMPT_TEMPLATES = {
    "landscape": "Một cảnh quan thiên nhiên đẹp với {description}, chất lượng cao, chi tiết sắc nét",
    "portrait": "Chân dung của {description}, ánh sáng đẹp, chất lượng điện ảnh",
    "action": "Cảnh hành động {description}, chuyển động mượt mà, góc quay đẹp",
    "animal": "Một con {animal} {description}, chuyển động tự nhiên, môi trường đẹp"
}

# Cài đặt GPU theo yêu cầu
GPU_CONFIGS = {
    "basic": {
        "gpu": "A10G",
        "memory": "24GB",
        "suitable_for": "Video ngắn, độ phân giải thấp"
    },
    "standard": {
        "gpu": "A100", 
        "memory": "40GB",
        "suitable_for": "Video 720P, 5 giây"
    },
    "premium": {
        "gpu": "A100",
        "memory": "80GB", 
        "suitable_for": "Video dài, độ phân giải cao"
    }
}
