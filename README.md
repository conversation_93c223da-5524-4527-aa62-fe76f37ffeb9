# Wan 2.2 5B Video Generation trên Modal

Dự án này thiết lập Wan 2.2 5B để chạy trên hạ tầng đám mây Modal với GPU mạnh mẽ.

## Tính năng

- 🎥 **Tạo Video AI**: Chạy Wan 2.2 5B trên GPU A100 40GB của Modal
- 📦 **Lưu trữ bền vững**: Models và video được lưu trong Modal volumes
- 🔄 **Tự động mở rộng**: Chỉ trả tiền khi sử dụng
- 🎨 **Dễ sử dụng**: Script Python đơn giản để tạo video
- 📋 **Hỗ trợ Workflow**: Sử dụng workflow ComfyUI tùy chỉnh
- 🌐 **Đa ngôn ngữ**: Hỗ trợ prompt tiếng Việt và tiếng Anh

## Bắt đầu nhanh

### 1. Y<PERSON><PERSON> cầu

- Python 3.11+
- <PERSON><PERSON><PERSON> <PERSON><PERSON> (đăng ký tại [modal.com](https://modal.com))

### 2. <PERSON><PERSON><PERSON> đặt

```bash
# Clone hoặc tải project này
# Điều hướng đến thư mục project

# Cài đặt dependencies
pip install -r requirements.txt

# Xác thực với Modal
modal token new
```

### 3. Thiết lập

Chạy script cài đặt tự động:

```bash
python cai_dat_wan22.py
```

Script này sẽ:
- Deploy ứng dụng Wan 2.2 lên Modal
- Tải xuống model Wan 2.2 5B (~20GB)
- Thiết lập lưu trữ bền vững

### 4. Tạo Video

#### Tạo video từ text:
```bash
python tao_video.py --prompt "Một chú mèo trắng đang chơi trên bãi biển"
```

#### Với tham số tùy chỉnh:
```bash
python tao_video.py \
  --prompt "Một người đang nhảy múa" \
  --size "704*1280" \
  --seed 123
```

#### Tạo video từ ảnh:
```bash
python tao_video.py \
  --image "anh.jpg" \
  --prompt "chuyển động nhẹ nhàng, tự nhiên"
```

## Lệnh thủ công

### Deploy app:
```bash
modal deploy wan22_modal.py
```

### Cài đặt model:
```bash
modal run wan22_modal.py::setup
```

### Tạo video trực tiếp với Modal:
```bash
modal run wan22_modal.py::text_to_video --prompt "mô tả video"
```

## Cấu trúc Project

```
├── wan22_modal.py           # Ứng dụng Modal chính
├── cai_dat_wan22.py         # Script cài đặt tự động
├── tao_video.py             # Script tạo video
├── kiem_tra_wan22.py        # Script kiểm tra hệ thống
├── requirements.txt         # Dependencies Python
├── modal.toml              # Cấu hình Modal project
├── workflows/              # File workflow ComfyUI
│   ├── wan22_text_to_video.json    # Workflow text-to-video
│   └── wan22_image_to_video.json   # Workflow image-to-video
├── config/                 # File cấu hình
│   ├── wan22_settings.py   # Cài đặt Wan 2.2
│   └── models.yaml         # Định nghĩa model
└── README.md               # File này
```

## Cấu hình

### Tùy chọn GPU

Cấu hình mặc định sử dụng GPU A100 40GB của Modal. Bạn có thể thay đổi GPU trong `wan22_modal.py`:

```python
gpu=modal.gpu.A10G()         # 24GB VRAM
gpu=modal.gpu.A100(size="40GB")  # 40GB VRAM
gpu=modal.gpu.A100(size="80GB")  # 80GB VRAM
```

### Models

Model được tự động tải xuống vào Modal volumes:
- **Model Wan 2.2**: `/root/models/Wan2.2-TI2V-5B/`
- **Outputs**: `/root/outputs/`

### Workflow tùy chỉnh

1. Tạo workflow trong ComfyUI và export thành JSON
2. Lưu vào thư mục `workflows/`
3. Sử dụng với ComfyUI (chưa được tích hợp hoàn toàn)

## Chi phí

Giá Modal (ước tính):
- **GPU A100 40GB**: ~$2.40/giờ
- **Lưu trữ**: ~$0.10/GB/tháng
- **Băng thông**: Có gói miễn phí

App sử dụng idle timeout (10 phút) để giảm thiểu chi phí.

## Xử lý sự cố

### Lỗi xác thực
```bash
modal token new
```

### Lỗi tải model
```bash
modal run wan22_modal.py::setup
```

### Lỗi tạo video
Kiểm tra logs Modal:
```bash
modal logs wan22-video-app
```

### Kiểm tra hệ thống
```bash
python kiem_tra_wan22.py
```

## Sử dụng nâng cao

### Tạo video hàng loạt

Tạo script để tạo nhiều video:

```python
import subprocess

prompts = [
    "Một chú mèo đang chơi",
    "Cảnh hoàng hôn trên biển",
    "Người đang nhảy múa"
]

for prompt in prompts:
    subprocess.run([
        "python", "tao_video.py",
        "--prompt", prompt
    ])
```

### Tích hợp API

Các hàm Modal có thể được gọi từ script Python khác:

```python
from wan22_modal import generate_video_t2v

result = generate_video_t2v.remote("mô tả video của bạn")
print(result)
```

### Template prompt

Sử dụng template có sẵn:

```bash
# Cảnh quan
python tao_video.py --template landscape --prompt "núi cao và thác nước"

# Hành động
python tao_video.py --template action --prompt "xe đua tốc độ"

# Động vật
python tao_video.py --template animal --prompt "chó con đang chạy"
```

## Hỗ trợ

- Tài liệu Modal: [docs.modal.com](https://docs.modal.com)
- Wan 2.2 GitHub: [github.com/Wan-Video/Wan2.2](https://github.com/Wan-Video/Wan2.2)
- Wan 2.2 HuggingFace: [huggingface.co/Wan-AI/Wan2.2-TI2V-5B](https://huggingface.co/Wan-AI/Wan2.2-TI2V-5B)

## Giấy phép

Project này là mã nguồn mở. Wan 2.2 và Modal có giấy phép riêng của họ.
