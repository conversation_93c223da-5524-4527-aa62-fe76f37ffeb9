#!/usr/bin/env python3
"""
Script kiểm tra hệ thống Wan 2.2 5B
<PERSON><PERSON><PERSON> <PERSON>h mọi thứ hoạt động đúng
"""

import subprocess
import sys
import json
from pathlib import Path

def chay_lenh(cmd, mo_ta="", capture_output=True):
    """Chạy lệnh và trả về kết quả"""
    print(f"🔄 {mo_ta}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            check=True, 
            capture_output=capture_output, 
            text=True
        )
        print(f"✅ {mo_ta} - OK")
        return result.stdout if capture_output else True
    except subprocess.CalledProcessError as e:
        print(f"❌ {mo_ta} - Lỗi")
        if capture_output:
            print(f"Chi tiết lỗi: {e.stderr}")
        return None

def kiem_tra_modal_auth():
    """Kiểm tra xác thực Modal"""
    result = subprocess.run("modal token current", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ Xác thực <PERSON> - OK")
        return True
    else:
        print("❌ Xác thực Modal - Lỗi")
        print("Chạy: modal token new")
        return False

def kiem_tra_modal_app():
    """Kiểm tra app Modal đã được deploy"""
    result = chay_lenh("modal app list", "Kiểm tra Modal apps")
    if result and "wan22-video-app" in result:
        print("✅ App Wan 2.2 đã được deploy - OK")
        return True
    else:
        print("❌ App Wan 2.2 chưa được deploy")
        return False

def kiem_tra_workflow_files():
    """Kiểm tra file workflow"""
    workflow_dir = Path("workflows")
    if not workflow_dir.exists():
        print("❌ Thư mục workflows không tồn tại")
        return False
    
    json_files = list(workflow_dir.glob("*.json"))
    if not json_files:
        print("❌ Không tìm thấy file workflow")
        return False
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print(f"✅ Workflow {json_file.name} - JSON hợp lệ")
        except json.JSONDecodeError as e:
            print(f"❌ Workflow {json_file.name} - JSON không hợp lệ: {e}")
            return False
    
    return True

def kiem_tra_requirements():
    """Kiểm tra các package cần thiết đã được cài"""
    required_packages = ["modal", "requests", "pillow", "numpy", "torch"]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ Package {package} - Đã cài")
        except ImportError:
            print(f"❌ Package {package} - Chưa cài")
            return False
    
    return True

def kiem_tra_cau_truc_project():
    """Kiểm tra cấu trúc project"""
    required_files = [
        "wan22_modal.py",
        "tao_video.py",
        "cai_dat_wan22.py", 
        "kiem_tra_wan22.py",
        "requirements.txt"
    ]
    
    required_dirs = [
        "workflows",
        "config"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ File {file_path} - Có")
        else:
            print(f"❌ File {file_path} - Thiếu")
            return False
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ Thư mục {dir_path} - Có")
        else:
            print(f"❌ Thư mục {dir_path} - Thiếu")
            return False
    
    return True

def kiem_tra_modal_volumes():
    """Kiểm tra Modal volumes"""
    result = chay_lenh("modal volume list", "Kiểm tra Modal volumes")
    if result:
        if "wan22-models" in result and "wan22-outputs" in result:
            print("✅ Modal volumes - OK")
            return True
        else:
            print("⚠️ Modal volumes chưa được tạo (sẽ tự động tạo khi chạy lần đầu)")
            return True
    return False

def chay_test_co_ban():
    """Chạy test tạo video cơ bản"""
    print("\n🧪 Test tạo video cơ bản")
    print("Test này sẽ mất vài phút và có thể tốn phí Modal.")
    
    response = input("Tiếp tục test? (y/N): ")
    if response.lower() != 'y':
        print("⏭️ Bỏ qua test tạo video")
        return True
    
    # Test với prompt đơn giản
    cmd = 'python tao_video.py --prompt "test video ngắn" --preview'
    result = chay_lenh(cmd, "Test preview prompt", capture_output=False)
    
    if result:
        print("✅ Test preview thành công!")
        
        # Hỏi có muốn test thật không
        response2 = input("Chạy test tạo video thật? (y/N): ")
        if response2.lower() == 'y':
            cmd_real = 'modal run wan22_modal.py::text_to_video --prompt "test video"'
            result_real = chay_lenh(cmd_real, "Test tạo video thật", capture_output=False)
            return result_real is not None
    
    return result is not None

def main():
    """Hàm chính kiểm tra"""
    print("🧪 Kiểm Tra Hệ Thống Wan 2.2 5B")
    print("=" * 50)
    
    tests = [
        ("Cấu trúc Project", kiem_tra_cau_truc_project),
        ("Packages Python", kiem_tra_requirements),
        ("File Workflow", kiem_tra_workflow_files),
        ("Xác thực Modal", kiem_tra_modal_auth),
        ("App Modal", kiem_tra_modal_app),
        ("Modal Volumes", kiem_tra_modal_volumes),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Kiểm tra: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} thất bại")
    
    print(f"\n📊 Kết quả: {passed}/{total} tests thành công")
    
    if passed == total:
        print("🎉 Tất cả tests đều thành công!")
        
        # Chạy test tạo video
        chay_test_co_ban()
        
        print("\n✅ Hệ thống Wan 2.2 5B sẵn sàng sử dụng!")
        print("\nCách sử dụng:")
        print("1. python tao_video.py --prompt 'mô tả video'")
        print("2. python tao_video.py --image anh.jpg --prompt 'chuyển động'")
        print("3. Xem Modal dashboard: https://modal.com/apps")
        
    else:
        print(f"\n❌ {total - passed} tests thất bại")
        print("Vui lòng sửa các lỗi trên trước khi sử dụng.")
        sys.exit(1)

if __name__ == "__main__":
    main()
