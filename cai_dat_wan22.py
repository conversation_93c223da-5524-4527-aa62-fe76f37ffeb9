#!/usr/bin/env python3
"""
Script cài đặt Wan 2.2 5B trên <PERSON>
Tự động hóa quá trình setup và kiểm tra
"""

import subprocess
import sys
import os
from pathlib import Path

def chay_lenh(cmd, mo_ta=""):
    """Chạy lệnh và xử lý lỗi"""
    print(f"🔄 {mo_ta}")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {mo_ta} - Thành công")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {mo_ta} - Thất bại")
        print(f"Lỗi: {e.stderr}")
        return None

def kiem_tra_modal_auth():
    """Kiểm tra xác thực Modal"""
    result = subprocess.run("modal token current", shell=True, capture_output=True, text=True)
    return result.returncode == 0

def cai_dat_requirements():
    """Cài đặt các thư viện Python cần thiết"""
    if not chay_lenh("pip install -r requirements.txt", "Cài đặt requirements"):
        return False
    return True

def thiet_lap_modal():
    """Thiết lập xác thực Modal"""
    if not kiem_tra_modal_auth():
        print("🔐 Cần xác thực Modal")
        print("Vui lòng chạy: modal token new")
        print("Sau đó truy cập URL để xác thực")
        return False
    
    print("✅ Modal đã được xác thực")
    return True

def deploy_app():
    """Deploy ứng dụng Wan 2.2 lên Modal"""
    if not chay_lenh("modal deploy wan22_modal.py", "Deploy ứng dụng Wan 2.2 lên Modal"):
        return False
    return True

def tai_model():
    """Tải xuống model Wan 2.2 5B"""
    print("📦 Đang tải model Wan 2.2 5B (có thể mất 10-30 phút)...")
    if not chay_lenh("modal run wan22_modal.py::setup", "Tải model Wan 2.2 5B"):
        return False
    return True

def kiem_tra_cau_truc_project():
    """Kiểm tra cấu trúc project"""
    required_files = [
        "wan22_modal.py",
        "tao_video.py", 
        "cai_dat_wan22.py",
        "requirements.txt"
    ]
    
    required_dirs = [
        "workflows",
        "config"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ File {file_path} - Có")
        else:
            print(f"❌ File {file_path} - Thiếu")
            return False
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ Thư mục {dir_path} - Có")
        else:
            print(f"❌ Thư mục {dir_path} - Thiếu")
            return False
    
    return True

def test_tao_video_co_ban():
    """Test tạo video cơ bản"""
    print("\n🧪 Test tạo video cơ bản...")
    print("Test này sẽ mất vài phút và có thể tốn phí Modal.")
    
    response = input("Tiếp tục test? (y/N): ")
    if response.lower() != 'y':
        print("⏭️ Bỏ qua test tạo video")
        return True
    
    # Chạy test tạo video ngắn
    cmd = 'modal run wan22_modal.py::text_to_video --prompt "test video ngắn"'
    result = chay_lenh(cmd, "Test tạo video", capture_output=False)
    
    return result is not None

def main():
    """Hàm chính để cài đặt"""
    print("🚀 Cài Đặt Wan 2.2 5B trên Modal")
    print("=" * 50)
    
    # Kiểm tra cấu trúc project
    if not kiem_tra_cau_truc_project():
        print("❌ Cấu trúc project không đúng")
        sys.exit(1)
    
    # Bước 1: Cài đặt requirements
    if not cai_dat_requirements():
        print("❌ Không thể cài đặt requirements")
        sys.exit(1)
    
    # Bước 2: Thiết lập Modal
    if not thiet_lap_modal():
        print("❌ Thiết lập Modal thất bại")
        sys.exit(1)
    
    # Bước 3: Deploy app
    if not deploy_app():
        print("❌ Deploy app thất bại")
        sys.exit(1)
    
    # Bước 4: Tải model
    print("📦 Tải model (quá trình này mất thời gian)...")
    if not tai_model():
        print("❌ Tải model thất bại")
        sys.exit(1)
    
    print("\n🎉 Cài đặt hoàn tất!")
    print("\nCách sử dụng:")
    print("1. Tạo video từ text:")
    print("   python tao_video.py --prompt 'mô tả video của bạn'")
    print("\n2. Tạo video từ ảnh:")
    print("   python tao_video.py --image anh.jpg --prompt 'mô tả chuyển động'")
    print("\n3. Xem thêm ví dụ:")
    print("   python tao_video.py")
    print("\n4. Kiểm tra Modal dashboard:")
    print("   https://modal.com/apps")
    
    # Test tùy chọn
    test_tao_video_co_ban()

if __name__ == "__main__":
    main()
