import modal
import os
import subprocess
import sys
import json
import time
from pathlib import Path

# <PERSON><PERSON><PERSON> ngh<PERSON>a <PERSON> app
app = modal.App("wan22-video-app")

# Tạo custom image với Wan 2.2 và dependencies
wan22_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install(
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libglib2.0-0", "libxrender1", "libgomp1", "build-essential"
    )
    .pip_install(
        "torch>=2.4.0", 
        "torchvision", 
        "torchaudio", 
        "--index-url", 
        "https://download.pytorch.org/whl/cu121"
    )
    .pip_install(
        "transformers",
        "accelerate", 
        "diffusers",
        "xformers",
        "opencv-python",
        "pillow",
        "numpy",
        "requests",
        "huggingface_hub",
        "modelscope",
        "imageio",
        "imageio-ffmpeg",
        "av",
        "decord",
        "einops",
        "safetensors"
    )
    .run_commands(
        "cd /root && git clone https://github.com/Wan-Video/Wan2.2.git",
        "cd /root/Wan2.2 && pip install -r requirements.txt"
    )
)

# Tạo persistent volumes cho models và outputs
models_volume = modal.Volume.from_name("wan22-models", create_if_missing=True)
outputs_volume = modal.Volume.from_name("wan22-outputs", create_if_missing=True)

@app.function(
    image=wan22_image,
    gpu=modal.gpu.A100(size="40GB"),  # Sử dụng A100 40GB cho Wan 2.2 5B
    volumes={
        "/root/models": models_volume,
        "/root/outputs": outputs_volume,
    },
    timeout=7200,  # 2 giờ timeout
    container_idle_timeout=600,  # 10 phút idle timeout
)
def setup_wan22_model():
    """Tải xuống model Wan 2.2 5B"""
    import subprocess
    from pathlib import Path
    
    models_dir = Path("/root/models")
    models_dir.mkdir(exist_ok=True)
    
    model_path = models_dir / "Wan2.2-TI2V-5B"
    
    if not model_path.exists():
        print("🔄 Đang tải xuống model Wan 2.2 5B...")
        
        # Sử dụng huggingface-cli để tải model
        cmd = [
            "huggingface-cli", "download", 
            "Wan-AI/Wan2.2-TI2V-5B",
            "--local-dir", str(model_path)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ Tải model thành công!")
            return f"Model đã được tải về: {model_path}"
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi khi tải model: {e.stderr}")
            return f"Lỗi: {e.stderr}"
    else:
        print("✅ Model đã tồn tại!")
        return f"Model đã có sẵn tại: {model_path}"

@app.function(
    image=wan22_image,
    gpu=modal.gpu.A100(size="40GB"),
    volumes={
        "/root/models": models_volume,
        "/root/outputs": outputs_volume,
    },
    timeout=3600,
    container_idle_timeout=600,
)
def generate_video_t2v(
    prompt: str,
    size: str = "1280*704",
    num_frames: int = 121,  # 5 giây ở 24fps
    seed: int = 42
):
    """Tạo video từ text prompt"""
    
    os.chdir("/root/Wan2.2")
    
    model_path = "/root/models/Wan2.2-TI2V-5B"
    output_dir = "/root/outputs"
    
    # Tạo thư mục output
    Path(output_dir).mkdir(exist_ok=True)
    
    # Tạo tên file output duy nhất
    timestamp = int(time.time())
    output_name = f"t2v_{timestamp}"
    
    print(f"🎬 Đang tạo video từ prompt: {prompt}")
    print(f"📐 Kích thước: {size}")
    print(f"🎞️ Số frame: {num_frames}")
    
    # Chạy lệnh generate
    cmd = [
        "python", "generate.py",
        "--task", "ti2v-5B",
        "--size", size,
        "--ckpt_dir", model_path,
        "--offload_model", "True",
        "--convert_model_dtype",
        "--t5_cpu",
        "--prompt", prompt,
        "--seed", str(seed),
        "--output_dir", output_dir,
        "--output_name", output_name
    ]
    
    try:
        print("🚀 Bắt đầu tạo video...")
        result = subprocess.run(
            cmd, 
            check=True, 
            capture_output=True, 
            text=True,
            cwd="/root/Wan2.2"
        )
        
        print("✅ Tạo video thành công!")
        
        # Tìm file video đã tạo
        output_files = list(Path(output_dir).glob(f"{output_name}*"))
        
        return {
            "status": "success",
            "prompt": prompt,
            "output_files": [str(f) for f in output_files],
            "message": "Video đã được tạo thành công!"
        }
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi tạo video: {e.stderr}")
        return {
            "status": "error",
            "error": e.stderr,
            "message": "Có lỗi xảy ra khi tạo video"
        }

@app.function(
    image=wan22_image,
    gpu=modal.gpu.A100(size="40GB"),
    volumes={
        "/root/models": models_volume,
        "/root/outputs": outputs_volume,
    },
    timeout=3600,
    container_idle_timeout=600,
)
def generate_video_i2v(
    prompt: str,
    image_path: str,
    size: str = "1280*704",
    num_frames: int = 121,
    seed: int = 42
):
    """Tạo video từ image và text prompt"""
    
    os.chdir("/root/Wan2.2")
    
    model_path = "/root/models/Wan2.2-TI2V-5B"
    output_dir = "/root/outputs"
    
    # Tạo thư mục output
    Path(output_dir).mkdir(exist_ok=True)
    
    # Tạo tên file output duy nhất
    timestamp = int(time.time())
    output_name = f"i2v_{timestamp}"
    
    print(f"🎬 Đang tạo video từ image và prompt: {prompt}")
    print(f"🖼️ Image: {image_path}")
    print(f"📐 Kích thước: {size}")
    
    # Chạy lệnh generate với image
    cmd = [
        "python", "generate.py",
        "--task", "ti2v-5B",
        "--size", size,
        "--ckpt_dir", model_path,
        "--offload_model", "True",
        "--convert_model_dtype",
        "--t5_cpu",
        "--image", image_path,
        "--prompt", prompt,
        "--seed", str(seed),
        "--output_dir", output_dir,
        "--output_name", output_name
    ]
    
    try:
        print("🚀 Bắt đầu tạo video...")
        result = subprocess.run(
            cmd, 
            check=True, 
            capture_output=True, 
            text=True,
            cwd="/root/Wan2.2"
        )
        
        print("✅ Tạo video thành công!")
        
        # Tìm file video đã tạo
        output_files = list(Path(output_dir).glob(f"{output_name}*"))
        
        return {
            "status": "success",
            "prompt": prompt,
            "image_path": image_path,
            "output_files": [str(f) for f in output_files],
            "message": "Video đã được tạo thành công!"
        }
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi tạo video: {e.stderr}")
        return {
            "status": "error",
            "error": e.stderr,
            "message": "Có lỗi xảy ra khi tạo video"
        }

# CLI functions
@app.local_entrypoint()
def setup():
    """Cài đặt model và dependencies"""
    print("🔧 Đang cài đặt Wan 2.2 5B model...")
    result = setup_wan22_model.remote()
    print(result)

@app.local_entrypoint()
def text_to_video(
    prompt: str = "Một chú mèo trắng đang chơi trên bãi biển",
    size: str = "1280*704",
    seed: int = 42
):
    """Tạo video từ text prompt"""
    print(f"🎬 Tạo video từ prompt: {prompt}")
    
    result = generate_video_t2v.remote(prompt, size, 121, seed)
    print(f"📊 Kết quả: {result}")
    return result

@app.local_entrypoint()
def image_to_video(
    prompt: str,
    image_path: str,
    size: str = "1280*704", 
    seed: int = 42
):
    """Tạo video từ image và text prompt"""
    print(f"🎬 Tạo video từ image: {image_path}")
    print(f"📝 Prompt: {prompt}")
    
    result = generate_video_i2v.remote(prompt, image_path, size, 121, seed)
    print(f"📊 Kết quả: {result}")
    return result

if __name__ == "__main__":
    print("🎥 Wan 2.2 5B Video Generation trên Modal")
    print("Sử dụng:")
    print("- modal run wan22_modal.py::setup (cài đặt model)")
    print("- modal run wan22_modal.py::text_to_video --prompt 'mô tả video'")
    print("- modal run wan22_modal.py::image_to_video --prompt 'mô tả' --image_path 'đường_dẫn_ảnh'")
